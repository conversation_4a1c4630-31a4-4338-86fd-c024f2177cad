import { ActionFunction, LoaderFunctionArgs, useLoaderData, Await } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import RowsViewRoute from "~/modules/rows/components/RowsViewRoute";
import { Rows_List } from "~/modules/rows/routes/Rows_List.server";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { getEntityPermission, getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import { serverTimingHeaders } from "~/modules/metrics/utils/defaultHeaders.server";
import { v2MetaFunction } from "~/utils/compat/v2MetaFunction";
import { Fragment, Suspense } from "react";
import { CompleteTableSkeleton } from "~/components/ui/skeletons";
export { serverTimingHeaders as headers };

export const meta: v2MetaFunction<Rows_List.LoaderData> = ({ data }) => data?.meta || [];
export const loader = (args: LoaderFunctionArgs) => Rows_List.loader(args);
export const action: ActionFunction = (args) => Rows_List.action(args);

export default function () {
  const data = useLoaderData<Rows_List.LoaderData>();
  const appOrAdminData = useAppOrAdminData();

  return (
    <Suspense fallback={
      <div className="space-y-4">
        {/* Page header skeleton */}
        <div className="flex items-center justify-between px-4 py-3">
          <div className="h-8 w-48 animate-pulse bg-gray-200 rounded" />
          <div className="flex gap-2">
            <div className="h-8 w-24 animate-pulse bg-gray-200 rounded" />
            <div className="h-8 w-32 animate-pulse bg-gray-200 rounded" />
          </div>
        </div>
        {/* Table skeleton */}
        <div className="rounded-lg border border-gray-200">
          <table className="min-w-full">
            <CompleteTableSkeleton
              headers={data.entity.properties.slice(0, 5).map(prop => ({
                name: prop.name,
                title: prop.title,
                value: () => "",
                hidden: false
              }))}
              actions={[{ name: "actions", title: "Actions" }]}
              onSelected={data.entity.hasBulkDelete}
              rows={10}
            />
          </table>
        </div>
      </div>
    }>
      <Await resolve={data.rowsData}>
        {(rowsData) => (
          <RowsViewRoute
            key={rowsData.entity.id}
            rowsData={rowsData}
            items={rowsData.items}
            routes={data.routes}
            saveCustomViews={true}
            permissions={{
              create: getUserHasPermission(appOrAdminData, getEntityPermission(rowsData.entity, "create")),
            }}
            currentSession={{
              user: appOrAdminData.user,
              isSuperAdmin: appOrAdminData.isSuperAdmin,
            }}
          />
        )}
      </Await>
    </Suspense>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
