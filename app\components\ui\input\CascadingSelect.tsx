import { useEffect, useState } from "react";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import InputSelect from "./InputSelect";

interface Props {
  property: PropertyWithDetails;
  parentProperty?: PropertyWithDetails;
  value: string | null;
  parentValue: string | null;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
}

export default function CascadingSelect({
  property,
  parentProperty,
  value,
  parentValue,
  onChange,
  disabled = false,
  required = false
}: Props) {
  const [options, setOptions] = useState<{value: string, name: string}[]>([]);
  const [loading, setLoading] = useState(false);

  // Check if this property depends on another property
  const dependsOnAttribute = property.attributes.find(
    attr => attr.name === "DependsOnProperty"
  );
  
  const isDependentProperty = !!dependsOnAttribute;
  const shouldFetchDynamicOptions = isDependentProperty && parentValue;

  useEffect(() => {
    async function fetchOptions() {
      setLoading(true);
      try {
        const url = shouldFetchDynamicOptions 
          ? `/api/property-options/${property.id}?parentValue=${parentValue}`
          : `/api/property-options/${property.id}`;
          
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.error) {
          console.error("API Error:", data.error);
          setOptions([]);
          return;
        }
        
        setOptions(data.options || []);
        
        // Reset value if it's not in the new options
        if (value && !data.options.some((opt: any) => opt.value === value)) {
          onChange(null);
        }
      } catch (error) {
        console.error("Failed to fetch options", error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    }
    
    if (isDependentProperty && !parentValue) {
      // Clear options if dependent but no parent value
      setOptions([]);
      if (value) onChange(null);
    } else {
      fetchOptions();
    }
  }, [property.id, parentValue, shouldFetchDynamicOptions, value, onChange, isDependentProperty]);

  const placeholder = isDependentProperty && !parentValue 
    ? `Select ${parentProperty?.title || 'parent'} first`
    : property.attributes.find(attr => attr.name === "Placeholder")?.value || `Select ${property.title}`;

  return (
    <InputSelect
      name={property.name}
      title={property.title}
      value={value}
      options={options}
      setValue={onChange}
      disabled={disabled || loading || (isDependentProperty && !parentValue)}
      required={required}
      placeholder={placeholder}
    />
  );
}
