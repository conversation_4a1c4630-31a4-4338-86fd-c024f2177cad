export enum PropertyAttributeName {
  HintText = "HintText", // Common
  HelpText = "HelpText", // Common
  Icon = "Icon", // Common
  Pattern = "pattern", // Text
  Min = "Min",
  Max = "Max",
  Step = "Step",
  Rows = "Rows",
  DefaultValue = "DefaultValue",
  Uppercase = "Uppercase",
  Lowercase = "Lowercase",
  Placeholder = "Placeholder",
  AcceptFileTypes = "AcceptFileTypes",
  MaxSize = "MaxSize",
  Editor = "editor",
  EditorLanguage = "EditorLanguage",
  EditorSize = "EditorSize",
  Columns = "Columns",
  Group = "Group",
  FormatNumber = "FormatNumber",
  FormatDate = "FormatDate",
  FormatTime = "FormatTime",
  FormatBoolean = "FormatBoolean",
  Separator = "Separator",
  SelectOptions = "SelectOptions",
  Password = "Password",
  // Cascading dropdown attributes
  DependsOnProperty = "DependsOnProperty", // Property name that this property depends on
  // Date/Time validation attributes
  MinDate = "MinDate",
  MaxDate = "MaxDate",
  DisablePastDates = "DisablePastDates",
  DisableFutureDates = "DisableFutureDates",
  MinTime = "MinTime",
  MaxTime = "MaxTime",
  // Range validation attributes
  MinRangeDuration = "MinRangeDuration",
  MaxRangeDuration = "MaxRangeDuration",
  MinStartDate = "MinStartDate",
  MaxStartDate = "MaxStartDate",
  MinEndDate = "MinEndDate",
  MaxEndDate = "MaxEndDate",
  MinStartTime = "MinStartTime",
  MaxStartTime = "MaxStartTime",
  MinEndTime = "MinEndTime",
  MaxEndTime = "MaxEndTime",
}
