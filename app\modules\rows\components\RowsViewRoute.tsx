import { Fragment, ReactNode, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, Outlet, useLocation, useNavigation, useParams, useSearchParams, useSubmit, useActionData } from "react-router";
import RowsList from "~/components/entities/rows/RowsList";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import InputFilters, { FilterDto } from "~/components/ui/input/InputFilters";
import TabsWithIcons from "~/components/ui/tabs/TabsWithIcons";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import { EntityViewWithDetails } from "~/utils/db/entities/entityViews.db.server";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { useAppData } from "~/utils/data/useAppData";
import EntityViewForm from "~/components/entities/views/EntityViewForm";
import { UserSimple } from "~/utils/db/users.db.server";
import SlideOverWideEmpty from "~/components/ui/slideOvers/SlideOverWideEmpty";
import { Rows_List } from "../routes/Rows_List.server";
import { toast } from "sonner";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import RunPromptFlowButtons from "~/modules/promptBuilder/components/run/RunPromptFlowButtons";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import TrashIcon from "~/components/ui/icons/TrashIcon";
import clsx from "clsx";
import InputSelect from "~/components/ui/input/InputSelect";
import AddButton from "~/custom/components/tables/AddButton";
import TableSearch from "~/custom/components/tables/TableSearch";
import DownloadCSVButton from "~/custom/components/Buttons/DownloadCSVButton";
import TabNavigation from "~/custom/components/RowOverviewRoute/components/TabNavigation";
import ViewButton from "~/custom/components/Buttons/ViewButton";
import ActionButton from "~/custom/components/actionButton/ActionButton";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import { Transition } from "@headlessui/react";
import DeselectIcon from "~/components/ui/icons/DeselectIcon";
import { useIsMobile } from "~/hooks/use-mobile";
import { useTableDeleteStore } from "~/hooks/useTableDeleteToggle";

interface Props {
  data?: any;
  isDrawer?: boolean;
  title?: ReactNode;
  rowsData: RowsApi.GetRowsData;
  items: RowWithDetails[];
  routes?: EntitiesApi.Routes;
  onNewRow?: () => void;
  onEditRow?: (item: RowWithDetails) => void;
  saveCustomViews?: boolean;
  permissions: {
    create: boolean;
  };
  currentSession: {
    user: UserSimple;
    isSuperAdmin: boolean;
  } | null;
}
export default function RowsViewRoute({
  title,
  isDrawer,
  rowsData,
  items,
  routes,
  onNewRow,
  onEditRow,
  saveCustomViews,
  permissions,
  currentSession,
  data,
}: Props) {
  const { t } = useTranslation();
  const actionData = useActionData<Rows_List.ActionData>();
  const appData = useAppData();
  const submit = useSubmit();
  const location = useLocation();
  const navigation = useNavigation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { state: isTableDeleteOpen, toggle: toggleTableDelete } = useTableDeleteStore();
  // const isNavigatingToEntity =
  //   navigation.location?.pathname &&
  //   navigation.location.pathname !== location.pathname &&
  //   navigation.location.pathname.split("/").length > location.pathname.split("/").length;

  // const [showTableSkeleton, setShowTableSkeleton] = useState(false);

  // useEffect(() => {

  //   if (rowsData.entity && navigation.state === "idle" && !navigation.location && !isNavigatingToEntity) {
  //     setShowTableSkeleton(true);
  //     const timer = setTimeout(() => {
  //       setShowTableSkeleton(false);
  //     }, 500);
  //     return () => clearTimeout(timer);
  //   } else {
  //     setShowTableSkeleton(false);
  //   }
  // }, [rowsData.entity?.id, navigation.state, navigation.location, isNavigatingToEntity]);

  const confirmDeleteRows = useRef<RefConfirmModal>(null);

  const [bulkActions, setBulkActions] = useState<string[]>([]);

  const [view, setView] = useState(rowsData.currentView?.layout ?? searchParams.get("view") ?? "table");
  const [filters, setFilters] = useState<FilterDto[]>([]);

  const [showCustomViewModal, setShowCustomViewModal] = useState(false);
  const [editingView, setEditingView] = useState<EntityViewWithDetails | null>(null);

  const [selectedRows, setSelectedRows] = useState<RowWithDetails[]>([]);

  const [searchInput, setSearchInput] = useState<string>("");
  const [opened, setOpened] = useState(false);
  const [tableFilterOptions, setTableFilterOptions] = useState<any[]>([]);
  const [selectedTableFilters, setSelectedTableFilters] = useState<any>();
  const params: any = useParams();
  const entityName = params.entity;
  const groupName = params.group;
  const tenantName = params.tenant;

  useEffect(() => {
    setFilters(EntityHelper.getFilters({ t, entity: rowsData.entity, pagination: rowsData.pagination }));
    const bulkActions: string[] = [];
    if (rowsData.entity.hasBulkDelete) {
      bulkActions.push("bulk-delete");
    }
    setBulkActions(bulkActions);
  }, [rowsData, t]);
  const handleFilter = () => {
    setOpened(!opened);
  };
  useEffect(() => {
    const newView = rowsData.currentView?.layout ?? searchParams.get("view") ?? "table";
    setView(newView);
  }, [searchParams, rowsData.entity, rowsData.currentView?.layout]);

  useEffect(() => {
    if (actionData?.error) {
      toast.error(actionData.error);
    } else if (actionData?.success) {
      toast.success(actionData.success);
      setSelectedRows([]);
    } else if (actionData?.rowsDeleted) {
      setSelectedRows((rows) => rows.filter((row) => !actionData?.rowsDeleted?.includes(row.id)));
    }
    if (actionData?.updatedView) {
      setShowCustomViewModal(false);
      setEditingView(null);
    }
  }, [actionData]);

  useEffect(() => {
    setShowCustomViewModal(false);
    setEditingView(null);
  }, [searchParams]);

  const searchablePropertiesCount = rowsData.entity.properties.filter((item) => item.isSearchable).length;

  function filteredItems() {
    const isSearchable = rowsData.entity.properties.filter((item) => item.isSearchable).map((item) => item.id);
    if (!searchInput) return items;
    return items.filter((item) =>
      item?.values?.some((value) => {
        const id = value?.propertyId;
        const type = rowsData.entity.properties.filter((item) => item.id === id).map((item) => item.type);
        if (!isSearchable.includes(id)) return false;
        const input = searchInput.toLowerCase().trim();
        switch (type[0]) {
          case 0:
            return String(value?.numberValue ?? "")
              .toLowerCase()
              .includes(input);
          case 1:
            return value?.textValue?.toLowerCase().includes(input);
          case 2:
            return String(value?.dateValue ?? "")
              .toLowerCase()
              .includes(input);
          case 10:
            return String(value?.booleanValue ?? "")
              .toLowerCase()
              .includes(input);
          default:
            return false;
        }
      })
    );
  }

  function onCreateView() {
    setShowCustomViewModal(true);
    setEditingView(null);
  }

  function onUpdateView() {
    setShowCustomViewModal(true);
    setEditingView(rowsData.currentView);
  }

  function isCurrenView(view: EntityViewWithDetails) {
    return rowsData.currentView?.id === view.id;
  }

  function canUpdateCurrentView() {
    if (currentSession?.isSuperAdmin) {
      return true;
    }
    if (!rowsData.currentView) {
      return false;
    }
    if (rowsData.currentView.userId === currentSession?.user.id) {
      return true;
    }
    if (appData?.currentTenant?.id && rowsData.currentView.tenantId === appData?.currentTenant.id && appData?.isSuperUser) {
      return true;
    }
    return false;
  }

  function capitalize(str: string) {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  useEffect(() => {
    let tableFilters = rowsData.entity.properties
      .filter((item) => item?.isTableFilter)
      .map((item) => ({ name: item.name, options: item.options.map((option) => ({ value: `${option.value}`, name: ` ${option.value}` })) }))
      .slice(0, 2);
    tableFilters.map((item) => {
      item.options = [{ value: "", name: t("shared.all") }, ...item.options];
      return item;
    });
    setTableFilterOptions(tableFilters);
  }, []);

  const handleTableFilter = (e: any, name: string) => {
    const updatedFilters = { ...selectedTableFilters };

    if (e === "fallback_value") {
      delete updatedFilters[name];
    } else {
      updatedFilters[name] = e;
    }
    setSelectedTableFilters(updatedFilters);
    const searchParams = new URLSearchParams(updatedFilters).toString();
    setSearchParams(searchParams);
  };

  function onDeleteSelectedRows() {
    confirmDeleteRows.current?.show(t("shared.confirmDelete"), t("shared.delete"), t("shared.cancel"), t("shared.warningCannotUndo"));
  }

  const handlePrioritySelect = (status: string, itemId: string) => {
    const form = new FormData();
    form.set("action", "edit");
    form.set("priority", status);
    // if (itemId) {
    //   form.set("itemId", itemId);
    // }

    submit(form, {
      navigate: false,
      method: "post",
      action: `/app/${tenantName}/g/${groupName}/${entityName}/${itemId}/edit`,
    });
  };

  function onDeleteSelectedRowsConfirmed() {
    const form = new FormData();
    form.set("action", "bulk-delete");
    selectedRows.forEach((item) => {
      form.append("rowIds[]", item.id);
    });
    submit(form, {
      method: "post",
    });
  }
  const searchPlaceholder = `Search ${rowsData.entity.title}`;

useEffect(() => {
  const shouldShow = rowsData?.entity?.hasBulkDelete && selectedRows?.length > 0 && rowsData?.entity?.views?.length <= 2;
  if (shouldShow && !isTableDeleteOpen) {
    toggleTableDelete(); 
  } else if (!shouldShow && isTableDeleteOpen) {
    toggleTableDelete(); 
  }
}, [selectedRows, isTableDeleteOpen, rowsData]);
  //   const [viewCounts, setViewCounts] = useState<Record<string, number>>({});

  // // Fetch counts when views change
  // useEffect(() => {
  //   const fetchCounts = async () => {
  //     const counts: Record<string, number> = {};
  //     for (const view of rowsData.views) {
  //       const response = await EntitiesApi.count({
  //         entity: rowsData.entity.name,
  //         view: view.name,
  //       });
  //       counts[view.name] = response.count;
  //     }
  //     setViewCounts(counts);
  //   };
  //   fetchCounts();
  // }, [rowsData.views, rowsData.entity.name]);

  const isMobile = useIsMobile();
  return (
    <>
      <div className="sticky mx-auto">
        <div className={`flex flex-col ${rowsData?.entity?.views?.length > 2 ? "h-[120px]" : ""} `}>
          <div className="flex h-[68px] items-center justify-between gap-2 px-1.5">
            <div className="ml-[10px] flex flex-row items-center justify-between gap-3 text-lg leading-none font-bold tracking-normal max-[769px]:flex-col max-[769px]:items-start">
              {title ?? (
                <h3 className="text-secondary-foreground flex flex-1 items-center truncate pt-2 text-lg font-bold">{`${t(rowsData.entity.titlePlural)}`}</h3>
              )}
            </div>

            {/* <div className="flex flex-row gap-3 max-sm:mr-0">
              <div className="flex gap-[10px]">
                {tableFilterOptions.map((item) => {
                  return (
                    <div key={item.name} className="w-44">
                      <InputSelect
                        name="tableFilter-1"
                        prefixLabel={capitalize(item.name)}
                        //  title={capitalize(item.name)}
                        value={selectedTableFilters?.[item.name] || ""}
                        setValue={(e) => {
                          handleTableFilter(e, item.name);
                        }}
                        options={item.options}
                        disabled={false}
                      />
                    </div>
                  );
                })}
 
                <TableSearch value={searchInput} setValue={setSearchInput} placeholder={searchPlaceholder} className="h-[32px] w-[342px]" />
              </div>
 
              {rowsData.entity.slug === "agent" && <AddButton label="Upload File" to={`${location.pathname}/import-xlsx`} className="bg-primary" />}
              <DownloadCSVButton rowsData={rowsData} routes={routes} searchParams={searchParams.toString()} />
              <div className="">
 
              { filters.length > 0 && <InputFilters  filters={filters} handleFilter={handleFilter}/>}
             
              </div>
              {permissions.create && (
                <AddButton
                  label={<span className="sm:text-sm">{`+ Add New ${rowsData.entity?.title}`}</span>}
                  className="custom-class-for-add-button"
                  onClick={onNewRow}
                  disabled={!permissions.create}
                  to={!onNewRow ? "new" : undefined}
                />
              )}
              <RunPromptFlowButtons type="list" promptFlows={rowsData.promptFlows} className="p-0.5" />
            </div> */}

            <div className="flex w-full min-w-0 flex-wrap items-center justify-end gap-x-2 gap-y-2 overflow-x-auto pt-3 pl-4">
              {tableFilterOptions.map((item) => (
                <div key={item.name} className="w-44 flex-shrink-0">
                  <InputSelect
                    name="tableFilter-1"
                    prefixLabel={capitalize(item.name)}
                    value={selectedTableFilters?.[item.name] || ""}
                    setValue={(e) => {
                      handleTableFilter(e, item.name);
                    }}
                    options={item.options}
                    disabled={false}
                  />
                </div>
              ))}
              {/* {!isMobile && <TableSearch value={searchInput} setValue={setSearchInput} placeholder={searchPlaceholder} className="flex-shrink-0" />} */}
              {!isMobile && searchablePropertiesCount > 0 && (
                <TableSearch value={searchInput} setValue={setSearchInput} placeholder={searchPlaceholder} className="flex-shrink-0" />
              )}
              {rowsData.entity.slug === "agent" && (
                <AddButton label="Upload File" to={`${location.pathname}/import-xlsx`} className="bg-primary flex-shrink-0" />
              )}
              <DownloadCSVButton rowsData={rowsData} routes={routes} searchParams={searchParams.toString()} className="flex-shrink-0" />
              {filters.length > 0 && <InputFilters filters={filters} Opened={opened} handleFilter={handleFilter} className="flex-shrink-0" />}
              {permissions.create && (
                <AddButton
                  label={<span className="sm:text-sm">{`+ Add New ${rowsData.entity?.title}`}</span>}
                  className="custom-class-for-add-button"
                  onClick={onNewRow}
                  disabled={!permissions.create}
                  to={!onNewRow ? "new" : undefined}
                />
              )}
              <RunPromptFlowButtons type="list" promptFlows={rowsData.promptFlows} className="flex-shrink-0 p-0.5" />
            </div>
          </div>

          {isMobile && (
            <TableSearch value={searchInput} setValue={setSearchInput} placeholder={searchPlaceholder} className="mx-3 mb-[13px] flex-shrink-0 px-1" />
          )}
          {rowsData.views.length > 2 ? (
            // ✅ TABS LAYOUT WITH OVERLAY
            <div className="">
              <div className="sticky top-0 z-100 mx-4 rounded-[8px] bg-white">
                {/* ✅ Absolute overlay bulk bar */}
                <Transition
                  show={rowsData?.entity?.hasBulkDelete && selectedRows?.length > 0}
                  enter="transition-all duration-300 ease-out"
                  enterFrom="opacity-0 -translate-y-2"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition-all duration-200 ease-in"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 -translate-y-2"
                >
                  <div className="absolute top-0 right-0 left-0 z-20 flex h-[56px] items-center justify-between rounded-[8px] border-b border-b-zinc-100 bg-[#D6E5FF]/90 px-4 shadow-md backdrop-blur-sm">
                    {/* Left: text */}
                    <p className="mr-3 text-xs whitespace-nowrap text-gray-900">
                      {selectedRows?.length || 0} {rowsData.entity?.titlePlural || ""} selected
                    </p>

                    {/* Right: buttons in a flex row with spacing */}
                    <div className="flex items-center gap-3">
                      <DeselectIconButton onClick={() => setSelectedRows([])} />
                      <DeleteIconButton onClick={onDeleteSelectedRows} />
                    </div>
                  </div>
                </Transition>

                {/* ✅ Tabs bar below overlay */}
                <div className="border-input flex h-[56px] items-center justify-between rounded-[8px] border">
                  <TabNavigation
                    className="h-full flex-grow !pl-0"
                    tabs={rowsData.views.map((item) => {
                      const searchParams = new URLSearchParams(location.search);
                      searchParams.set("v", item.name);
                      searchParams.delete("page");
                      return {
                        name: t(item.title),
                        href: location.pathname + "?" + searchParams.toString(),
                        current: isCurrenView(item),
                        count: isCurrenView(item) ? rowsData.pagination?.totalItems : undefined,
                      };
                    })}
                  />
                </div>
              </div>
            </div>
          ) : (
            isTableDeleteOpen && (
              <Transition
                show={isTableDeleteOpen}
                enter="transition-transform transition-opacity duration-400 ease-[cubic-bezier(0.4,0,0.2,1)] transition-all"
                enterFrom="opacity-0 -translate-y-2"
                enterTo="opacity-100 translate-y-0"
                leave="transition-transform transition-opacity duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] duration-200 ease-in"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 -translate-y-2"
              >
                <section
                  className={`mx-3 flex items-center justify-between rounded-[8px] border-b border-solid border-b-zinc-100 bg-[#D6E5FF]/24 py-2 pr-3 pl-4 transition-opacity duration-300 ease-in-out max-md:flex-col max-md:items-start max-md:gap-2 max-sm:p-2.5 ${
                    rowsData?.entity?.hasBulkDelete && selectedRows?.length > 0 ? "opacity-100" : "hidden "
                  }`}
                >
                  {/* Left: selected text */}
                  <p className="mr-3 text-sm whitespace-nowrap text-gray-900">
                    {selectedRows?.length || 0} {rowsData.entity?.titlePlural || ""} selected
                  </p>

                  {/* Right: buttons with spacing */}
                  <div className="flex items-center gap-3">
                    <DeselectIconButton onClick={() => setSelectedRows([])} />
                    <DeleteIconButton onClick={onDeleteSelectedRows} />
                  </div>
                </section>
              </Transition>
            )
          )}
        </div>

        <Outlet />

        <ConfirmModal ref={confirmDeleteRows} onYes={onDeleteSelectedRowsConfirmed} />
      </div>
      <div className="z-50">
        <SlideOverWideEmpty
          title={editingView ? "Edit view" : `New ${t(rowsData.entity.title)} view`}
          className="sm:max-w-2xl"
          open={showCustomViewModal}
          onClose={() => setShowCustomViewModal(false)}
        >
          {showCustomViewModal && (
            <EntityViewForm
              isDrawer={true}
              entity={rowsData.entity}
              tenantId={appData.currentTenant?.id ?? null}
              userId={currentSession?.user.id ?? null}
              item={editingView}
              canDelete={true}
              onClose={() => setShowCustomViewModal(false)}
              actionNames={{
                create: "view-create",
                update: "view-edit",
                delete: "view-delete",
              }}
              isSystem={false}
              showViewType={currentSession?.isSuperAdmin ?? false}
            />
          )}
        </SlideOverWideEmpty>
      </div>
      <RowsList
        view={view as "table" | "board" | "grid" | "card"}
        entity={rowsData.entity}
        items={filteredItems()}
        routes={routes}
        pagination={rowsData.pagination}
        onEditRow={onEditRow}
        currentView={rowsData.currentView}
        selectedRows={selectedRows}
        onSelected={!bulkActions?.length ? undefined : (rows) => setSelectedRows(rows)}
        searchInput={searchInput}
        entityTitle={rowsData.entity?.title}
        onNewRow={onNewRow}
        permissionCreate={permissions.create}
        opened={opened}
        setOpened={setOpened}
        filters={filters}
        // loading={showTableSkeleton}
        handleFilter={handleFilter}
      />
    </>
  );
}

function DeleteIconButton({ onClick }: { onClick: () => void }) {
  const navigation = useNavigation();
  return (
    <ButtonSecondary
      type="button"
      className={clsx(
        "text-foreground inline-flex cursor-pointer items-center justify-center rounded-sm px-4 py-2 ring-1 ring-[#E6E6E6] hover:bg-[#F8DDDD]",
        navigation.state === "submitting" && navigation.formData?.get("action") === "bulk-delete" && "cursor-not-allowed opacity-75"
      )}
      disabled={navigation.state !== "idle"}
      onClick={onClick}
    >
      {navigation.state === "submitting" && navigation.formData?.get("action") === "bulk-delete" ? (
        <>
          <svg className="mr-2 -ml-1 h-4 w-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Deleting...
        </>
      ) : (
        <>
          <TrashIcon />
          Delete
        </>
      )}
    </ButtonSecondary>
  );
}

function DeselectIconButton({ onClick }: { onClick: () => void }) {
  return (
    <ButtonSecondary
      onClick={onClick}
      className="text-foreground pointer-cursor inline-flex items-center justify-center rounded-sm p-2 hover:bg-gray-100 hover:text-gray-700 focus:outline-none"
    >
      <DeselectIcon />
      Deselect
    </ButtonSecondary>
  );
}
