import { LoaderFunctionArgs, json } from "react-router";
import { db } from "~/utils/db.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const propertyId = params.propertyId;
  const url = new URL(request.url);
  const parentValue = url.searchParams.get("parentValue");
  
  if (!propertyId) {
    return json({ error: "Property ID is required" }, { status: 400 });
  }
  
  try {
    // Get the property to check if it has dependencies
    const property = await db.property.findUnique({
      where: { id: propertyId },
      include: { 
        attributes: true,
        options: {
          orderBy: { order: 'asc' }
        }
      }
    });
    
    if (!property) {
      return json({ error: "Property not found" }, { status: 404 });
    }
    
    // Check if this property depends on another property
    const dependsOnAttribute = property.attributes.find(
      attr => attr.name === "DependsOnProperty"
    );
    
    if (!dependsOnAttribute || !parentValue) {
      // Return all options if no dependency or no parent value
      return json({ 
        options: property.options.map(opt => ({
          value: opt.value,
          name: opt.name || opt.value
        }))
      });
    }
    
    // Filter options based on parent value using "parentValue:childValue" format
    const filteredOptions = property.options
      .filter(option => option.value.startsWith(`${parentValue}:`))
      .map(option => ({
        value: option.value.split(':')[1], // Remove parent prefix
        name: option.name || option.value.split(':')[1]
      }));
    
    return json({ options: filteredOptions });
    
  } catch (error) {
    console.error("Error fetching property options:", error);
    return json({ error: "Failed to fetch options" }, { status: 500 });
  }
}
